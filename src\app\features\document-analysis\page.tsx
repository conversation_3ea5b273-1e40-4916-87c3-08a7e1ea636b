'use client'

import React, { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { FileUpload } from '@/components/ui/file-upload'
import { AnalysisProgress } from '@/components/ui/analysis-progress'
import { Download, Play, X, ChevronUp, History } from 'lucide-react'
import { UseCaseSelector } from '@/components/features/use-case-selector'
import { SectionsManager } from '@/components/features/sections-manager'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import { createAnalysisFormData } from '@/lib/document-utils'
import { showAnalysisCompleteNotification, showAnalysisFailedNotification } from '@/lib/notifications'
import { TutorialOverlay, TutorialTrigger } from '@/components/onboarding/tutorial-overlay'
import { WelcomeMessage } from '@/components/onboarding/welcome-message'
import { PaymentSuccessModal } from '@/components/onboarding/payment-success-modal'
import { AuthModal } from '@/components/auth/auth-modal'
import {
  documentAnalysisTutorialSteps,
  shouldShowOnboarding,
  shouldShowWelcomeMessage,
  markOnboardingCompleted,
  markWelcomeMessageSeen,
  markTutorialCompleted
} from '@/lib/tutorial'


export default function DocumentAnalysisPage() {
  const { data: session, status } = useSession()
  const [credits, setCredits] = useState<number | null>(null)
  const [isNewUser, setIsNewUser] = useState<boolean>(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [selectedUseCase, setSelectedUseCase] = useState<string | null>(null)
  const [selectedSections, setSelectedSections] = useState<string[]>([])
  const [uploadedFile, setUploadedFile] = useState<File | null>(null)
  const [language, setLanguage] = useState('English')
  const [customLanguage, setCustomLanguage] = useState('')
  const [reportLength, setReportLength] = useState('long')

  const [analysisProgress, setAnalysisProgress] = useState(0)
  const [analysisResult, setAnalysisResult] = useState<any>(null)
  const [showMarkdownViewer, setShowMarkdownViewer] = useState(false)
  const [errorMessage, setErrorMessage] = useState<string | null>(null)
  const [successMessage, setSuccessMessage] = useState<string | null>(null)
  const [currentOperationId, setCurrentOperationId] = useState<string | null>(null)
  const [eventSource, setEventSource] = useState<EventSource | null>(null)
  const [progressTimer, setProgressTimer] = useState<NodeJS.Timeout | null>(null)
  const [isProgressTimerActive, setIsProgressTimerActive] = useState(false)

  // Tutorial state
  const [showWelcomeModal, setShowWelcomeModal] = useState(false)
  const [showTutorial, setShowTutorial] = useState(false)

  // Authentication modal state
  const [authModal, setAuthModal] = useState<{ isOpen: boolean; mode: 'signin' | 'signup' }>({
    isOpen: false,
    mode: 'signin'
  })

  // Load persisted state on component mount
  useEffect(() => {
    const savedState = localStorage.getItem('documentAnalysisState')
    if (savedState) {
      try {
        const state = JSON.parse(savedState)
        const now = Date.now()

        // Only restore form state (not processing state) to prevent auto-analysis on refresh
        if (state.timestamp && (now - state.timestamp) < 10 * 60 * 1000) {
          // Always restore form selections
          setSelectedUseCase(state.selectedUseCase)
          setSelectedSections(state.selectedSections || [])
          setLanguage(state.language || 'English')
          setCustomLanguage(state.customLanguage || '')
          setReportLength(state.reportLength || 'long')

          // Restore file info if available (but not if it was processing)
          if (state.uploadedFileName && !state.isProcessing) {
            // Create a placeholder file object for display
            const placeholderFile = new File([''], state.uploadedFileName, { type: 'application/pdf' })
            Object.defineProperty(placeholderFile, 'size', { value: state.uploadedFileSize || 0 })
            setUploadedFile(placeholderFile)
          }

          // If there was a processing state, check if it's still valid instead of auto-resuming
          if (state.isProcessing && state.currentOperationId) {
            // Check operation status instead of auto-resuming processing
            checkOperationStatusOnRefresh(state.currentOperationId)
          }

          console.log('Restored form state from localStorage (no auto-processing):', {
            operationId: state.currentOperationId,
            wasProcessing: state.isProcessing,
            formRestored: true
          })
        } else {
          // Clear old state
          localStorage.removeItem('documentAnalysisState')
        }
      } catch (error) {
        console.error('Error restoring analysis state:', error)
        localStorage.removeItem('documentAnalysisState')
      }
    }
  }, [])

  // Save state to localStorage when key values change
  useEffect(() => {
    if (currentOperationId && isProcessing) {
      const state = {
        currentOperationId,
        isProcessing,
        analysisProgress,
        selectedUseCase,
        selectedSections,
        language,
        customLanguage,
        reportLength,
        uploadedFileName: uploadedFile?.name,
        uploadedFileSize: uploadedFile?.size,
        timestamp: Date.now()
      }
      localStorage.setItem('documentAnalysisState', JSON.stringify(state))
    } else if (!isProcessing) {
      // Clear state when not processing
      localStorage.removeItem('documentAnalysisState')
    }
  }, [currentOperationId, isProcessing, analysisProgress, selectedUseCase, selectedSections, language, customLanguage, reportLength, uploadedFile])







  // Auto-dismiss messages after 5 seconds
  useEffect(() => {
    if (errorMessage) {
      const timer = setTimeout(() => setErrorMessage(null), 5000)
      return () => clearTimeout(timer)
    }
  }, [errorMessage])

  useEffect(() => {
    if (successMessage) {
      const timer = setTimeout(() => setSuccessMessage(null), 5000)
      return () => clearTimeout(timer)
    }
  }, [successMessage])

  // Fetch user credits
  React.useEffect(() => {
    if (session) {
      console.log('🔍 Fetching credits for session:', session.user?.email)
      fetch('/api/credits/balance')
        .then(res => res.json())
        .then(data => {
          console.log('💰 Credits fetched:', data.balance)
          setCredits(data.balance)
        })
        .catch(err => console.error('Failed to fetch credits:', err))
    }
  }, [session])

  // Check for new user state from URL params or session storage
  React.useEffect(() => {
    if (session) {
      // Check URL params for new user indicator
      const urlParams = new URLSearchParams(window.location.search)
      const isNewUserFromUrl = urlParams.get('newUser') === 'true'

      // Check session storage for new user indicator
      const isNewUserFromStorage = sessionStorage.getItem('isNewUser') === 'true'

      if (isNewUserFromUrl || isNewUserFromStorage) {
        setIsNewUser(true)
        console.log('🎉 New user detected - showing welcome message')

        // For new users, always show welcome message (ignore localStorage check)
        setShowWelcomeModal(true)

        // Clear the indicators after use
        sessionStorage.removeItem('isNewUser')
        if (isNewUserFromUrl) {
          // Clean up URL without refreshing
          const newUrl = new URL(window.location.href)
          newUrl.searchParams.delete('newUser')
          window.history.replaceState({}, '', newUrl.toString())
        }
      }
    }
  }, [session])


  // Cleanup SSE connection and progress timer on component unmount
  React.useEffect(() => {
    return () => {
      if (eventSource) {
        console.log('Cleaning up SSE connection on unmount')
        if (eventSource.readyState !== EventSource.CLOSED) {
          eventSource.close()
        }
      }
      if (progressTimer) {
        console.log('Cleaning up progress timer on unmount')
        clearInterval(progressTimer)
      }
    }
  }, [eventSource, progressTimer])

  // Tutorial handlers
  const handleWelcomeClose = () => {
    setShowWelcomeModal(false)
    markWelcomeMessageSeen()
    // Automatically start tutorial after welcome message
    setTimeout(() => {
      setShowTutorial(true)
    }, 500)
  }

  const handleStartTutorial = () => {
    setShowTutorial(true)
  }

  const handleTutorialComplete = () => {
    markOnboardingCompleted()
    markTutorialCompleted('document-analysis')
  }

  const handleTutorialClose = () => {
    setShowTutorial(false)
  }

  // Calculate estimated processing time based on file size
  const calculateEstimatedTime = (fileSizeBytes: number): number => {
    // Base time: 30 seconds for small files
    // Additional time: 15 seconds per MB (16MB = 250 seconds total)
    const fileSizeMB = fileSizeBytes / (1024 * 1024)
    const baseTime = 20 // 30 seconds base
    const additionalTime = Math.max(0, fileSizeMB - 1) * 15 // 15 seconds per MB after first MB
    return Math.min(baseTime + additionalTime, 300) // Cap at 5 minutes
  }

  // Format estimated time for display
  const formatEstimatedTime = (seconds: number): string => {
    if (seconds < 60) {
      return `${Math.round(seconds)} seconds`
    } else if (seconds < 120) {
      return `${Math.round(seconds / 60)} minute`
    } else {
      return `${Math.round(seconds / 60)} minutes`
    }
  }

  // Start progress timer with file-size-based estimation
  const startProgressTimer = (fileSizeBytes: number) => {
    // Prevent multiple timers from running
    if (isProgressTimerActive || progressTimer) {
      console.log('Progress timer already active, skipping')
      return
    }

    console.log('Starting progress timer for file size:', fileSizeBytes)
    setIsProgressTimerActive(true)

    const estimatedSeconds = calculateEstimatedTime(fileSizeBytes)
    const updateInterval = 500 // Update every 0.5 seconds for smoother progress

    // Define step durations - make them more proportional
    const step1Duration = Math.max(3, estimatedSeconds * 0.15)  // 15% of time for text extraction (min 3s)
    const step2Duration = Math.max(4, estimatedSeconds * 0.25)  // 25% of time for structure analysis (min 4s)
    const step3Duration = Math.max(5, estimatedSeconds * 0.35)  // 35% of time for insights (min 5s)
    const step4Duration = Math.max(3, estimatedSeconds * 0.25)  // 25% of time for finalizing (min 3s)

    const stepThresholds = [
      step1Duration,                                    // Step 2: Extracting document content (25%)
      step1Duration + step2Duration,                    // Step 3: Analyzing document structure (50%)
      step1Duration + step2Duration + step3Duration,   // Step 4: Generating insights (75%)
      step1Duration + step2Duration + step3Duration + step4Duration  // Step 5: Finalizing report (99%)
    ]

    let currentProgress = 0
    let elapsedSeconds = 0

    const timer = setInterval(() => {
      elapsedSeconds += 0.5

      // Calculate progress based on which step we're in
      if (elapsedSeconds <= stepThresholds[0]) {
        // Step 2: Extracting document content (0% to 25%)
        currentProgress = (elapsedSeconds / stepThresholds[0]) * 25
      } else if (elapsedSeconds <= stepThresholds[1]) {
        // Step 3: Analyzing document structure (25% to 50%)
        const stepProgress = (elapsedSeconds - stepThresholds[0]) / step2Duration
        currentProgress = 25 + (stepProgress * 25)
      } else if (elapsedSeconds <= stepThresholds[2]) {
        // Step 4: Generating insights (50% to 75%)
        const stepProgress = (elapsedSeconds - stepThresholds[1]) / step3Duration
        currentProgress = 50 + (stepProgress * 25)
      } else if (elapsedSeconds <= stepThresholds[3]) {
        // Step 5: Finalizing report (75% to 99%)
        const stepProgress = (elapsedSeconds - stepThresholds[2]) / step4Duration
        currentProgress = 75 + (stepProgress * 24) // Cap at 99%
      } else {
        // Cap at 99% until webhook completes
        currentProgress = 99
        clearInterval(timer)
        setProgressTimer(null)
        setIsProgressTimerActive(false)
      }

      setAnalysisProgress(Math.min(currentProgress, 99))
    }, updateInterval)

    setProgressTimer(timer)

    // Dynamic timeout: 3x estimated time, maximum 5 minutes
    const timeoutSeconds = Math.min(estimatedSeconds * 3, 300) // 3x estimated time, max 5 minutes
    const timeoutMs = timeoutSeconds * 1000

    setTimeout(() => {
      console.log('⏰ Progress timer timeout reached, checking operation status')
      clearInterval(timer)
      setProgressTimer(null)
      setIsProgressTimerActive(false)

      // Check if analysis completed during timeout
      if (currentOperationId) {
        checkOperationStatusOnRefresh(currentOperationId)
      }
    }, timeoutMs)
  }



  // Restart progress timer when state is restored and processing
  useEffect(() => {
    if (isProcessing && currentOperationId && uploadedFile && !progressTimer && !isProgressTimerActive) {
      console.log('Restarting progress timer for restored state')

      // Check if analysis is already completed before starting timer
      checkOperationStatusOnRefresh(currentOperationId).then(() => {
        // Only start timer if still processing after status check
        if (isProcessing && !progressTimer && !isProgressTimerActive) {
          startProgressTimer(uploadedFile.size)
        }
      })
    }
  }, [isProcessing, currentOperationId, uploadedFile, progressTimer, isProgressTimerActive])

  // Check operation status on page refresh instead of auto-resuming
  const checkOperationStatusOnRefresh = async (operationId: string) => {
    try {
      const response = await fetch(`/api/analyze/${operationId}`)
      if (response.ok) {
        const data = await response.json()

        if (data.success && data.markdownContent) {
          // Analysis completed while user was away - show results
          setAnalysisResult({
            metadata: data.metadata,
            markdownContent: data.markdownContent,
            docxUrl: data.output_file_blob_sas_url
          })
          setShowMarkdownViewer(true)
          localStorage.removeItem('documentAnalysisState')
        } else if (data.operationType === 'DOCUMENT_ANALYSIS' && !data.success) {
          // Analysis is still processing or failed - user can choose to resume or start fresh
          console.log('Previous analysis found, user can choose to resume or start fresh')
          // Don't auto-resume, let user decide
          localStorage.removeItem('documentAnalysisState')
        }
      } else {
        // Operation not found or error - clear state
        localStorage.removeItem('documentAnalysisState')
      }
    } catch (error) {
      console.error('Error checking operation status:', error)
      localStorage.removeItem('documentAnalysisState')
    }
  }


  // Unified cleanup for SSE, timer, and state
  const cleanupResources = (resetAnalysisResult = false) => {
    if (eventSource) {
      eventSource.close();
      setEventSource(null);
    }
    if (progressTimer) {
      clearInterval(progressTimer);
      setProgressTimer(null);
      setIsProgressTimerActive(false);
    }
    setIsProcessing(false);
    setAnalysisProgress(0);
    setCurrentOperationId(null);
    setErrorMessage(null);
    setSuccessMessage(null);
    if (resetAnalysisResult) {
      setAnalysisResult(null);
      setShowMarkdownViewer(false);
    }
    localStorage.removeItem('documentAnalysisState');
  };

  // Cancel analysis function
  const handleCancelAnalysis = () => {
    console.log('Cancelling analysis...');
    cleanupResources();
    setErrorMessage('Analysis cancelled. Note: The credit spent cannot be refunded.');
  };

  // Clean up analysis state when new file is uploaded
  const cleanupAnalysisState = () => {
    console.log('Cleaning up analysis state for new file upload');
    cleanupResources(true);
  };

  // Handle file selection with cleanup
  const handleFileSelect = (file: File) => {
    if (showMarkdownViewer || isProcessing || currentOperationId) {
      cleanupAnalysisState();
    }
    setUploadedFile(file);
  };

  const handleAnalysis = async () => {
    // Clear previous messages and state
    setErrorMessage(null)
    setSuccessMessage(null)
    setCurrentOperationId(null)

    if (!session) {
      setErrorMessage('Please sign in to use this feature')
      return
    }

    if (!selectedUseCase) {
      setErrorMessage('Please select an analysis type')
      return
    }

    if (!uploadedFile) {
      setErrorMessage('Please upload a document first')
      return
    }

    if (credits === null || credits < 1) {
      setErrorMessage('Insufficient credits. Please purchase more credits.')
      return
    }

    setIsProcessing(true)
    setAnalysisProgress(0)
    setAnalysisResult(null)
    setShowMarkdownViewer(false)

    // Start progress timer based on file size
    if (uploadedFile) {
      startProgressTimer(uploadedFile.size)
    }

    try {
      // Create form data for file upload using utility
      const formData = createAnalysisFormData({
        file: uploadedFile,
        analysisType: selectedUseCase,
        sections: selectedSections,
        language: language === 'custom' ? customLanguage : language,
        reportLength: reportLength as 'short' | 'medium' | 'long'
      })

      // Start analysis using Azure Function (credits are deducted in the API)
      const response = await fetch('/api/analyze', {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to start analysis')
      }

      // Update credits in UI after successful analysis start
      setCredits(prev => prev ? prev - 1 : 0)

      // Trigger header credit refresh by dispatching a custom event
      window.dispatchEvent(new CustomEvent('creditBalanceChanged'))

      const { operationId } = await response.json()

      // Store operation ID for checking status later
      setCurrentOperationId(operationId)

      // Show success message
      setSuccessMessage('Analysis started successfully! You will be notified automatically when it completes.')

      // Immediately establish SSE connection for real-time updates
      console.log('🚀 Establishing SSE connection for operation:', operationId)

      // Clean up any existing connection first
      if (eventSource) {
        console.log('🧹 Cleaning up existing SSE connection before creating new one')
        eventSource.close()
        setEventSource(null)
      }

      console.log('🔗 Creating new EventSource for:', `/api/analyze/${operationId}/stream`)
      const es = new EventSource(`/api/analyze/${operationId}/stream`)
      console.log('🔗 EventSource created, setting state...')
      setEventSource(es)
      console.log('🔗 EventSource state set')

      es.onopen = () => {
        console.log('🔌 SSE connection opened for operation:', operationId)
        console.log('🔌 SSE readyState:', es.readyState)
        console.log('🔌 EventSource URL:', es.url)
        console.log('🔌 SSE connection established successfully at:', new Date().toISOString())
      }

      es.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          console.log('📨 SSE message received:', data)

          if (data.type === 'analysis_complete') {
            console.log('🎉 Analysis completed via SSE:', {
              success: data.success,
              hasMarkdown: !!data.markdownContent,
              hasDocxUrl: !!data.output_file_blob_sas_url,
              markdownLength: data.markdownContent?.length || 0
            })

            // Close SSE connection first
            es.close()
            setEventSource(null)

            if (data.success === true) {
              console.log('✅ Analysis completed successfully via webhook')

              // Stop progress timer immediately
              if (progressTimer) {
                clearInterval(progressTimer)
                setProgressTimer(null)
                setIsProgressTimerActive(false)
              }

              // Smoothly animate progress to 100% before showing results
              const currentProgress = analysisProgress
              const targetProgress = 100
              const animationDuration = 1000 // 1 second
              const steps = 20
              const stepDuration = animationDuration / steps
              const progressIncrement = (targetProgress - currentProgress) / steps

              let step = 0
              const progressAnimation = setInterval(() => {
                step++
                const newProgress = Math.min(currentProgress + (progressIncrement * step), 100)
                setAnalysisProgress(newProgress)

                if (step >= steps || newProgress >= 100) {
                  clearInterval(progressAnimation)
                  setAnalysisProgress(100)

                  // Wait a brief moment at 100% before showing results
                  setTimeout(() => {
                    setIsProcessing(false)
                    setCurrentOperationId(null)

                    const resultData = {
                      metadata: data.metadata,
                      markdownContent: data.markdownContent,
                      docxUrl: data.output_file_blob_sas_url
                    }

                    console.log('Setting analysis result:', {
                      hasMetadata: !!resultData.metadata,
                      hasMarkdown: !!resultData.markdownContent,
                      hasDocxUrl: !!resultData.docxUrl,
                      markdownLength: resultData.markdownContent?.length || 0
                    })

                    setAnalysisResult(resultData)

                    // Clear saved state on completion
                    localStorage.removeItem('documentAnalysisState')

                    // Show markdown viewer after progress animation
                    console.log('Showing markdown viewer')
                    setShowMarkdownViewer(true)

                    // Show browser notification
                    showAnalysisCompleteNotification(
                      uploadedFile?.name || 'Document',
                      () => setShowMarkdownViewer(true)
                    )
                  }, 300) // Brief pause at 100%
                }
              }, stepDuration)
            } else if (data.success === false) {
              console.log('❌ Analysis failed via webhook:', data.error || data.metadata?.error || 'Unknown error')
              console.log('🔌 Closing SSE connection due to webhook failure confirmation')

              // Stop progress timer immediately on failure
              if (progressTimer) {
                clearInterval(progressTimer)
                setProgressTimer(null)
                setIsProgressTimerActive(false)
              }

              // Close SSE connection
              es.close()
              setEventSource(null)
              setIsProcessing(false)
              setAnalysisProgress(0)
              setCurrentOperationId(null)

              // Clear saved state on failure
              localStorage.removeItem('documentAnalysisState')

              // Use error from webhook payload directly (prioritize 'error' field over metadata.error)
              const errorMsg = data.error || data.metadata?.error || 'Analysis failed. Please try again.'
              setErrorMessage(`Analysis failed: ${errorMsg}. Your credit has been refunded.`)

              // Show failure notification
              showAnalysisFailedNotification(
                uploadedFile?.name || 'Document',
                errorMsg
              )
            }
          } else if (data.type === 'analysis_timeout') {
            console.log('⏰ Analysis timeout')

            // Stop progress timer on timeout
            if (progressTimer) {
              clearInterval(progressTimer)
              setProgressTimer(null)
              setIsProgressTimerActive(false)
            }

            // Close SSE connection
            es.close()
            setEventSource(null)
            setIsProcessing(false)
            setAnalysisProgress(0)
            setCurrentOperationId(null)

            // Clear saved state on timeout
            localStorage.removeItem('documentAnalysisState')

            setErrorMessage('Analysis is taking longer than expected. Please try again or contact support if the issue persists.')

            // Show timeout notification
            showAnalysisFailedNotification(
              uploadedFile?.name || 'Document',
              'Analysis timeout - please try again'
            )
          } else if (data.type === 'connected') {
            console.log('Connected to SSE stream')
          } else if (data.type === 'ping') {
            // Keep-alive ping, do nothing
          }
        } catch (error) {
          console.error('❌ Error parsing SSE message:', error)
          console.error('Raw SSE data:', event.data)
        }
      }

      es.onerror = (error) => {
        console.error('❌ SSE connection error for operation:', operationId)
        console.error('❌ Error details:', error)
        console.error('❌ SSE readyState:', es.readyState)

        // Close connection and clean up immediately on any error
        es.close()
        setEventSource(null)

        // Stop progress timer immediately
        if (progressTimer) {
          clearInterval(progressTimer)
          setProgressTimer(null)
          setIsProgressTimerActive(false)
        }

        // Stop processing state and show error
        setIsProcessing(false)
        setAnalysisProgress(0)
        setCurrentOperationId(null)

        // Clear saved state
        localStorage.removeItem('documentAnalysisState')

        // Show error message without auto-retry
        setErrorMessage('Connection error occurred. Please try again or check your analysis history if the analysis was completed.')

        console.log('🔌 SSE connection closed due to error, analysis stopped')
      }

    } catch (error) {
      console.error('Analysis failed:', error)
      setErrorMessage('Analysis failed to start. Your credit has been refunded.')
      setIsProcessing(false)

      // Clean up progress timer on error
      if (progressTimer) {
        clearInterval(progressTimer)
        setProgressTimer(null)
      }
    }
  }



  // Show loading state while session is loading
  if (status === 'loading') {
    return (
      <div className="p-8 bg-gray-50 min-h-screen">
        <div className="max-w-4xl mx-auto text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  if (!session) {
    return (
      <div className="p-8 bg-gray-50 min-h-screen">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Document Analysis</h1>
          <p className="text-gray-600 mb-8">Please sign in to access document analysis features.</p>
          <Button
            onClick={() => setAuthModal({ isOpen: true, mode: 'signin' })}
            className="text-white transition-all duration-300 transform hover:scale-105 !rounded-md bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 shadow-lg hover:shadow-purple-500/50 !rounded-md hover:!rounded-md"
          >
            Sign In
          </Button>
        </div>

        {/* Auth Modal */}
        <AuthModal
          isOpen={authModal.isOpen}
          onClose={() => setAuthModal({ ...authModal, isOpen: false })}
          initialMode={authModal.mode}
        />
      </div>
    )
  }

  return (
    <div className="p-4 sm:p-8 bg-gray-50 min-h-screen">
      <div className="max-w-6xl mx-auto">
        {/* Markdown Viewer Modal */}
        {(() => {
          // ...existing code...
          return showMarkdownViewer && analysisResult;
        })() && (
          <div className="auth-modal-backdrop flex items-center justify-center p-4">
            <div
              className="absolute inset-0"
              onClick={() => setShowMarkdownViewer(false)}
            />
            <div className="auth-modal-content max-w-6xl w-full max-h-[90vh] flex flex-col transform transition-all duration-500 ease-out animate-in slide-in-from-bottom-8 relative z-10">
              {/* Header */}
              <div className="flex items-center justify-between p-6 border-b border-gray-200">
                <h2 className="text-2xl font-bold text-gray-900">Analysis Results</h2>
                <button
                  onClick={() => setShowMarkdownViewer(false)}
                  className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                >
                  <X className="w-6 h-6 text-gray-500" />
                </button>
              </div>

              {/* Content */}
              <div className="flex-1 overflow-y-auto p-8">
                <div className="prose prose-lg max-w-none mx-auto">
                  <ReactMarkdown
                    remarkPlugins={[remarkGfm]}
                    components={{
                      h1: ({children}) => <h1 className="text-3xl font-bold text-gray-900 mb-6 mt-8 first:mt-0">{children}</h1>,
                      h2: ({children}) => <h2 className="text-2xl font-semibold text-gray-800 mb-4 mt-8">{children}</h2>,
                      h3: ({children}) => <h3 className="text-xl font-medium text-gray-700 mb-3 mt-6">{children}</h3>,
                      p: ({children}) => <p className="text-gray-600 mb-4 leading-relaxed break-words">{children}</p>,
                      ul: ({children}) => <ul className="list-disc list-inside mb-6 text-gray-600 space-y-1">{children}</ul>,
                      ol: ({children}) => <ol className="list-decimal list-inside mb-6 text-gray-600 space-y-1">{children}</ol>,
                      li: ({children}) => <li className="mb-1 break-words">{children}</li>,
                      strong: ({children}) => <strong className="font-semibold text-gray-800">{children}</strong>,
                      code: ({children}) => <code className="bg-gray-100 px-2 py-1 rounded text-sm font-mono break-all">{children}</code>,
                      blockquote: ({children}) => <blockquote className="border-l-4 border-purple-500 pl-6 py-2 italic text-gray-600 mb-6 bg-purple-50 rounded-r-lg">{children}</blockquote>,
                      table: ({children}) => (
                        <div className="overflow-x-auto mb-6 border border-gray-200 rounded-lg">
                          <table className="min-w-full divide-y divide-gray-200">
                            {children}
                          </table>
                        </div>
                      ),
                      thead: ({children}) => <thead className="bg-gray-50">{children}</thead>,
                      tbody: ({children}) => <tbody className="bg-white divide-y divide-gray-200">{children}</tbody>,
                      tr: ({children}) => <tr className="hover:bg-gray-50">{children}</tr>,
                      th: ({children}) => <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200 last:border-r-0">{children}</th>,
                      td: ({children}) => <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r border-gray-200 last:border-r-0 break-words">{children}</td>,
                      hr: () => <hr className="my-8 border-gray-300" />
                    }}
                  >
                    {analysisResult.markdownContent}
                  </ReactMarkdown>
                </div>
              </div>

              {/* Footer with Actions */}
              <div className="border-t border-gray-200 p-6 bg-gray-50">
                <div className="flex flex-col sm:flex-row gap-3">
                  <Button
                    onClick={() => {
                      const blob = new Blob([analysisResult.markdownContent], { type: 'text/markdown' })
                      const url = URL.createObjectURL(blob)
                      const a = document.createElement('a')
                      a.href = url
                      a.download = 'analysis-report.md'
                      a.click()
                      URL.revokeObjectURL(url)
                    }}
                    variant="outline"
                    className="flex-1"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Download Markdown (.md)
                  </Button>
                  <Button
                    onClick={() => {
                      window.open(analysisResult.docxUrl, '_blank')
                    }}
                    className="flex-1 bg-purple-600 hover:bg-purple-700 text-white"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Download Word (.docx)
                  </Button>
                  <Button
                    onClick={() => setShowMarkdownViewer(false)}
                    variant="outline"
                    className="sm:w-auto"
                  >
                    Close
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Header */}
        <div className="mb-8 flex justify-between items-start">
          <div>
            <div className="flex items-center gap-2 mb-2">
              <h1 className="text-3xl font-bold text-gray-900">Document Analysis</h1>
              <TutorialTrigger onClick={handleStartTutorial} />
            </div>
            <p className="text-gray-600">
              Choose your analysis type and configure sections to extract targeted insights from your documents.
            </p>
          </div>
          <Link href="/features/document-analysis/history">
            <Button variant="outline" className="flex items-center gap-2">
              <History className="w-4 h-4" />
              History
            </Button>
          </Link>
        </div>



        <div className="flex justify-center">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-5xl w-full">
          {/* Left Column - Configuration */}
          <div className="space-y-6">
            {/* Use Case Selection */}
            <div data-tutorial="analysis-type-selector">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">1. Select Analysis Type</h2>
              <UseCaseSelector
                selectedUseCase={selectedUseCase}
                onUseCaseChange={setSelectedUseCase}
              />
            </div>

            {/* Sections Configuration */}
            <div data-tutorial="sections-manager">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">2. Configure Analysis Sections</h2>
              <SectionsManager
                selectedUseCase={selectedUseCase}
                selectedSections={selectedSections}
                onSectionsChange={setSelectedSections}
              />
            </div>


          </div>

          {/* Right Column - Settings, Upload & Analysis */}
          <div className="space-y-6">
            {/* Report Settings */}
            <div data-tutorial="report-settings">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">3. Report Settings</h2>
              <div className="bg-white rounded-lg shadow-sm border p-6 space-y-4">
                {/* Language Selection */}
                <div>
                  <label htmlFor="language" className="block text-sm font-medium text-gray-700 mb-2">
                    Report Language
                  </label>
                  <select
                    id="language"
                    value={language === 'custom' ? 'custom' : language}
                    onChange={(e) => {
                      if (e.target.value === 'custom') {
                        setLanguage('custom')
                        setCustomLanguage('')
                      } else {
                        setLanguage(e.target.value)
                        setCustomLanguage('')
                      }
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  >
                    <option value="English">English</option>
                    <option value="Chinese (Simplified)">中文简体 (Chinese Simplified)</option>
                    <option value="Chinese (Traditional)">中文繁體 (Chinese Traditional)</option>
                    <option value="Spanish">Español (Spanish)</option>
                    <option value="French">Français (French)</option>
                    <option value="German">Deutsch (German)</option>
                    <option value="Japanese">日本語 (Japanese)</option>
                    <option value="Korean">한국어 (Korean)</option>
                    <option value="Portuguese">Português (Portuguese)</option>
                    <option value="Italian">Italiano (Italian)</option>
                    <option value="Russian">Русский (Russian)</option>
                    <option value="custom">Other (specify below)</option>
                  </select>

                  {/* Custom Language Input */}
                  {language === 'custom' && (
                    <div className="mt-2">
                      <input
                        type="text"
                        placeholder="Enter your preferred language..."
                        value={customLanguage}
                        onChange={(e) => setCustomLanguage(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                      />
                    </div>
                  )}
                </div>

                {/* Report Length Selection */}
                <div>
                  <label htmlFor="reportLength" className="block text-sm font-medium text-gray-700 mb-2">
                    Report Length
                  </label>
                  <select
                    id="reportLength"
                    value={reportLength}
                    onChange={(e) => setReportLength(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  >
                    <option value="short">Short (500 words) - Concise Summary</option>
                    <option value="medium">Medium (1000 words) - Balanced Analysis</option>
                    <option value="long">Long (2000 words) - Comprehensive Insights</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Upload Area */}
            <div data-tutorial="file-upload">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">4. Upload Document</h2>
              <div className="bg-white rounded-lg shadow-sm border p-6">
                <FileUpload
                  onFileSelect={handleFileSelect}
                  onFileRemove={() => {
                    cleanupAnalysisState()
                    setUploadedFile(null)
                  }}
                  selectedFile={uploadedFile}
                  disabled={isProcessing}
                  showPreview={true}
                  showSupportedFormats={true}
                />
              </div>
            </div>



            {/* Progress Display */}
            {isProcessing && uploadedFile && (
              <AnalysisProgress
                status="processing"
                progress={analysisProgress}
                fileName={uploadedFile.name}
                estimatedTime={formatEstimatedTime(calculateEstimatedTime(uploadedFile.size))}
              />
            )}

            {/* Success Message - In Processing Area */}
            {successMessage && (
              <div className="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3 flex-1">
                    <p className="text-sm font-medium text-green-800">{successMessage}</p>
                  </div>
                  <div className="ml-auto pl-3">
                    <button
                      onClick={() => setSuccessMessage(null)}
                      className="inline-flex text-green-400 hover:text-green-600"
                    >
                      <X className="w-5 h-5" />
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Error Message - In Processing Area */}
            {errorMessage && (
              <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3 flex-1">
                    <p className="text-sm font-medium text-red-800">{errorMessage}</p>
                  </div>
                  <div className="ml-auto pl-3">
                    <button
                      onClick={() => setErrorMessage(null)}
                      className="inline-flex text-red-400 hover:text-red-600"
                    >
                      <X className="w-5 h-5" />
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Large File Reminder */}
            {isProcessing && uploadedFile && uploadedFile.size > 5 * 1024 * 1024 && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-blue-700">
                      <strong>Large file detected:</strong> This file is larger than 5MB and may take longer to process. Please be patient - we&apos;ll notify you once the analysis is completed.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Analysis Complete Notification */}
            {analysisResult && (
              <div className="bg-white rounded-lg shadow-sm border p-6">
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-green-800">
                          Analysis completed successfully!
                        </p>
                      </div>
                    </div>
                    <Button
                      onClick={() => setShowMarkdownViewer(true)}
                      className="bg-purple-600 hover:bg-purple-700 text-white"
                    >
                      <ChevronUp className="w-4 h-4 mr-2" />
                      View Results
                    </Button>
                  </div>
                </div>
              </div>
            )}



            {/* Action Button */}
            <div>
              {isProcessing ? (
                <div className="flex flex-col sm:flex-row gap-4 w-full">
                  <Button
                    disabled
                    className="flex-1 bg-purple-600 text-white px-6 py-3 text-sm font-medium cursor-not-allowed"
                  >
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Analyzing Document... {Math.floor(analysisProgress)}%
                  </Button>
                  <Button
                    onClick={handleCancelAnalysis}
                    variant="outline"
                    className="w-full sm:w-auto sm:flex-shrink-0 sm:px-6 px-6 py-3 text-sm font-medium border-red-300 text-red-600 hover:bg-red-50 hover:border-red-400"
                    title="Caution: Credits spent cannot be refunded"
                  >
                    <X className="w-4 h-4 mr-2" />
                    Cancel
                  </Button>
                </div>
              ) : (
                <Button
                  onClick={handleAnalysis}
                  disabled={credits === 0 || !selectedUseCase || !uploadedFile}
                  className="w-full bg-purple-600 hover:bg-purple-700 text-white px-8 py-4 text-lg font-medium"
                  data-tutorial="start-analysis"
                >
                  <Play className="w-5 h-5 mr-3" />
                  Start Analysis (1 Credit)
                </Button>
              )}
            </div>
          </div>
        </div>
        </div>

        {/* Tutorial Components */}
        <WelcomeMessage
          isOpen={showWelcomeModal}
          onClose={handleWelcomeClose}
          userCredits={credits || 0}
        />

        <TutorialOverlay
          isOpen={showTutorial}
          onClose={handleTutorialClose}
          steps={documentAnalysisTutorialSteps}
          onComplete={handleTutorialComplete}
        />

      </div>
    </div>
  )
}
