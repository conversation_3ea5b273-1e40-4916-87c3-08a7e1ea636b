'use client'

import React from 'react'
import { Button } from '@/components/ui/button'
import { Download, X } from 'lucide-react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'

interface AnalysisResult {
  metadata?: any;
  markdownContent: string;
  docxUrl?: string;
}

interface MarkdownViewerModalProps {
  isOpen: boolean;
  analysisResult: AnalysisResult | null;
  onClose: () => void;
}

export function MarkdownViewerModal({ 
  isOpen, 
  analysisResult, 
  onClose 
}: MarkdownViewerModalProps) {
  if (!isOpen || !analysisResult) {
    return null;
  }

  const handleDownloadMarkdown = () => {
    const blob = new Blob([analysisResult.markdownContent], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'analysis-report.md';
    a.click();
    URL.revokeObjectURL(url);
  };

  const handleDownloadDocx = () => {
    if (analysisResult.docxUrl) {
      const a = document.createElement('a');
      a.href = analysisResult.docxUrl;
      a.download = 'analysis-report.docx';
      a.click();
    }
  };

  return (
    <div className="auth-modal-backdrop flex items-center justify-center p-4">
      <div
        className="absolute inset-0"
        onClick={onClose}
      />
      <div className="auth-modal-content max-w-6xl w-full max-h-[90vh] flex flex-col transform transition-all duration-500 ease-out animate-in slide-in-from-bottom-8 relative z-10">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 rounded-t-2xl">
          <h2 className="text-2xl font-bold text-gray-900">Analysis Results</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X className="w-6 h-6 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          <div className="prose prose-lg max-w-none" style={{ wordBreak: 'break-word' }}>
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              components={{
                // Custom styling for tables with responsive cells
                table: ({ children }) => (
                  <div className="overflow-x-auto my-6">
                    <table className="w-full border-collapse border border-gray-300" style={{ tableLayout: 'auto' }}>
                      {children}
                    </table>
                  </div>
                ),
                thead: ({ children }) => (
                  <thead className="bg-gray-50">
                    {children}
                  </thead>
                ),
                th: ({ children }) => (
                  <th
                    className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border border-gray-300"
                    style={{
                      minWidth: '120px',
                      maxWidth: '300px',
                      width: 'auto',
                      wordBreak: 'break-word',
                      overflowWrap: 'anywhere'
                    }}
                  >
                    <div className="break-words">
                      {children}
                    </div>
                  </th>
                ),
                td: ({ children }) => (
                  <td
                    className="px-4 py-3 text-sm text-gray-900 border border-gray-300 align-top"
                    style={{
                      minWidth: '120px',
                      maxWidth: '300px',
                      width: 'auto',
                      wordBreak: 'break-word',
                      overflowWrap: 'anywhere',
                      verticalAlign: 'top'
                    }}
                  >
                    <div
                      className="break-words leading-relaxed"
                      style={{
                        wordBreak: 'break-word',
                        overflowWrap: 'anywhere',
                        whiteSpace: 'pre-wrap',
                        hyphens: 'auto'
                      }}
                    >
                      {children}
                    </div>
                  </td>
                ),
                // Custom styling for headings
                h1: ({ children }) => (
                  <h1 className="text-3xl font-bold text-gray-900 mb-6 pb-2 border-b border-gray-200">
                    {children}
                  </h1>
                ),
                h2: ({ children }) => (
                  <h2 className="text-2xl font-semibold text-gray-800 mt-8 mb-4">
                    {children}
                  </h2>
                ),
                h3: ({ children }) => (
                  <h3 className="text-xl font-semibold text-gray-800 mt-6 mb-3">
                    {children}
                  </h3>
                ),
                // Custom styling for lists
                ul: ({ children }) => (
                  <ul className="list-disc list-inside space-y-2 my-4">
                    {children}
                  </ul>
                ),
                ol: ({ children }) => (
                  <ol className="list-decimal list-inside space-y-2 my-4">
                    {children}
                  </ol>
                ),
                li: ({ children }) => (
                  <li className="text-gray-700 leading-relaxed">
                    {children}
                  </li>
                ),
                // Custom styling for paragraphs
                p: ({ children }) => (
                  <p className="text-gray-700 leading-relaxed mb-4">
                    {children}
                  </p>
                ),
                // Custom styling for blockquotes
                blockquote: ({ children }) => (
                  <blockquote className="border-l-4 border-purple-500 pl-4 py-2 my-4 bg-purple-50 italic">
                    {children}
                  </blockquote>
                ),
                // Custom styling for code blocks
                code: ({ children, className }) => {
                  const isInline = !className;
                  if (isInline) {
                    return (
                      <code className="bg-gray-100 text-purple-600 px-1 py-0.5 rounded text-sm font-mono">
                        {children}
                      </code>
                    );
                  }
                  return (
                    <code className="block bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto text-sm font-mono">
                      {children}
                    </code>
                  );
                },
                pre: ({ children }) => (
                  <pre className="bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto my-4">
                    {children}
                  </pre>
                ),
              }}
            >
              {analysisResult.markdownContent}
            </ReactMarkdown>
          </div>
        </div>

        {/* Footer with Actions */}
        <div className="border-t border-gray-200 p-6 bg-gray-50 rounded-b-2xl">
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              onClick={handleDownloadMarkdown}
              variant="outline"
              className="flex items-center space-x-2"
            >
              <Download className="w-4 h-4" />
              <span>Download Markdown</span>
            </Button>
            
            {analysisResult.docxUrl && (
              <Button
                onClick={handleDownloadDocx}
                className="flex items-center space-x-2 bg-purple-600 hover:bg-purple-700"
              >
                <Download className="w-4 h-4" />
                <span>Download DOCX</span>
              </Button>
            )}
            
            <Button
              onClick={onClose}
              variant="ghost"
              className="sm:ml-auto"
            >
              Close
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
