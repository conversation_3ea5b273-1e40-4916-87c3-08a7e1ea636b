'use client'

import React, { useState, useEffect, useRef } from 'react'
import { useTranslation } from '@/hooks/use-translation'

interface FeatureStep {
  id: number
  title: string
  description: string
  image?: string
  video?: {
    resultPreview: string
    docxPreview: string
  }
  imageAlt: string
}

export function ScrollFeaturesSection() {
  const { t } = useTranslation('homepage')
  const [activeStep, setActiveStep] = useState(0)
  const [showResultPreview, setShowResultPreview] = useState(true)
  const stepsRef = useRef<(HTMLDivElement | null)[]>([])

  // Define the five steps with your images and videos
  const steps: FeatureStep[] = [
    {
      id: 1,
      title: "Step 1: Select Your Analysis Template",
      description: "Choose from our professionally designed templates tailored for different document types and analysis needs. Whether you're analyzing financial reports, legal documents, or general business documents, our templates provide the perfect starting point for accurate and comprehensive analysis.",
      image: "/home-page-feature-section/select-template.png",
      imageAlt: "Select Template interface"
    },
    {
      id: 2,
      title: "Step 2: Choose Document Sections",
      description: "Pick the specific sections of your document that you want to analyze for targeted insights. Our intelligent section detection helps you focus on the most relevant parts of your document, ensuring you get precise analysis results that matter most to your workflow.",
      image: "/home-page-feature-section/select-section.png",
      imageAlt: "Select Section interface"
    },
    {
      id: 3,
      title: "Step 3: Select Output Language",
      description: "Choose your preferred language for the analysis output to ensure clear understanding and seamless integration with your workflow. Our multilingual support ensures that language barriers never limit your document analysis capabilities.",
      image: "/home-page-feature-section/select-language.png",
      imageAlt: "Select Language interface"
    },
    {
      id: 4,
      title: "Step 4: Upload Document",
      description: "Upload your document and let our AI analyze it. We support PDF, DOCX, PPTX files up to 300 pages. Our secure upload process ensures your documents are processed safely and efficiently.",
      image: "/home-page-feature-section/upload-document.png",
      imageAlt: "Upload Document interface"
    },
    {
      id: 5,
      title: "Step 5: Discover Powerful Insights",
      description: "Unlock comprehensive analysis results with intelligent insights that transform your documents into actionable intelligence. Experience seamless switching between preview modes to visualize your results exactly how you need them.",
      video: {
        resultPreview: "/home-page-feature-section/result-preview.mp4",
        docxPreview: "/home-page-feature-section/docx-preview.mp4"
      },
      imageAlt: "Analysis Results interface"
    }
  ]

  useEffect(() => {
    const handleScroll = () => {
      const scrollY = window.scrollY
      const windowHeight = window.innerHeight
      const viewportCenter = scrollY + windowHeight / 2

      let closestIndex = 0
      let closestDistance = Infinity

      stepsRef.current.forEach((stepElement, index) => {
        if (!stepElement) return

        const rect = stepElement.getBoundingClientRect()
        const elementTop = rect.top + scrollY
        const elementCenter = elementTop + rect.height / 2
        const distance = Math.abs(elementCenter - viewportCenter)

        if (distance < closestDistance) {
          closestDistance = distance
          closestIndex = index
        }
      })

      setActiveStep(closestIndex)
    }

    // Throttle scroll events for better performance
    let ticking = false
    const throttledScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll()
          ticking = false
        })
        ticking = true
      }
    }

    window.addEventListener('scroll', throttledScroll, { passive: true })
    window.addEventListener('resize', throttledScroll, { passive: true })

    // Initial check
    handleScroll()

    return () => {
      window.removeEventListener('scroll', throttledScroll)
      window.removeEventListener('resize', throttledScroll)
    }
  }, [])

  return (
    <section className="py-20 bg-gradient-to-br from-purple-50 via-blue-50 to-pink-50 overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16 animate-fade-in-up">
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
            Simple Steps to Get Started
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Our intuitive interface makes document analysis effortless. Follow these simple steps to unlock powerful insights from your documents.
          </p>
        </div>

        <div className="space-y-16 lg:space-y-24">
          {steps.map((step, index) => (
            <div
              key={step.id}
              ref={(el) => {
                if (el) stepsRef.current[index] = el
              }}
              className="flex flex-col lg:flex-row gap-8 lg:gap-12 xl:gap-16 items-center"
            >
              {/* Video step uses regular layout but with larger video */}
              {step.video ? (
                <>
                  {/* Left side - Step Content */}
                  <div className={`w-full lg:w-1/2 order-2 lg:order-1 transition-all duration-700 ease-in-out ${
                    activeStep === index ? 'opacity-100 transform translate-x-0' : 'opacity-50 transform translate-x-2'
                  }`}>
                    <div className="flex items-start space-x-4 lg:space-x-6 mb-6">
                      <div className="flex-shrink-0 w-12 h-12 lg:w-14 lg:h-14 rounded-full flex items-center justify-center text-white font-bold text-lg lg:text-xl bg-gradient-to-r from-purple-600 to-pink-600 shadow-lg">
                        {step.id}
                      </div>
                      <div className="flex-1">
                        <h3 className="text-xl lg:text-2xl font-bold mb-3 lg:mb-4 text-gray-900">
                          {step.title}
                        </h3>
                      </div>
                    </div>

                    <p className="text-base lg:text-lg leading-relaxed mb-6 text-gray-700">
                      {step.description}
                    </p>

                    {/* Features list for step 5 */}
                    <div className="space-y-2">
                      <ul className="space-y-2 text-sm text-gray-600">
                        <li className="flex items-center space-x-2">
                          <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                          <span>Comprehensive analysis</span>
                        </li>
                        <li className="flex items-center space-x-2">
                          <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                          <span>Multiple preview formats</span>
                        </li>
                        <li className="flex items-center space-x-2">
                          <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                          <span>Export options</span>
                        </li>
                      </ul>
                    </div>
                  </div>

                  {/* Right side - Larger Video */}
                  <div className="w-full lg:w-1/2 order-1 lg:order-2">
                    <div className="relative">
                      {/* Switch for video step - positioned above video */}
                      {activeStep === index && (
                        <div className="flex justify-center mb-4">
                          <div className="flex bg-gray-100 rounded-xl p-1 shadow-sm border border-gray-200">
                            <button
                              onClick={() => setShowResultPreview(true)}
                              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                                showResultPreview
                                  ? 'bg-white text-gray-900 shadow-sm'
                                  : 'text-gray-600 hover:text-gray-900'
                              }`}
                            >
                              Preview
                            </button>
                            <button
                              onClick={() => setShowResultPreview(false)}
                              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center space-x-2 ${
                                !showResultPreview
                                  ? 'bg-white text-gray-900 shadow-sm'
                                  : 'text-gray-600 hover:text-gray-900'
                              }`}
                            >
                              <img src="/home-page-feature-section/docx_icon.png" alt="DOCX" className="w-4 h-4" />
                              <span>DOCX</span>
                            </button>
                          </div>
                        </div>
                      )}

                      {/* Larger video container - bigger than step 4 */}
                      <div className={`relative bg-white rounded-xl lg:rounded-2xl shadow-xl lg:shadow-2xl overflow-hidden border border-gray-200 transition-all duration-700 ease-in-out ${
                        activeStep === index
                          ? 'opacity-100 shadow-2xl'
                          : 'opacity-30 shadow-lg'
                      }`}>
                        <div className="aspect-[16/10] relative p-4 lg:p-6 flex items-center justify-center bg-white">
                          <video
                            key={showResultPreview ? 'result' : 'docx'}
                            src={showResultPreview ? step.video.resultPreview : step.video.docxPreview}
                            autoPlay
                            muted
                            loop
                            playsInline
                            className="w-full h-full object-contain rounded-lg shadow-lg"
                          >
                            Your browser does not support the video tag.
                          </video>
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                /* Regular layout for other steps */
                <>
              {/* Left side - Step Content */}
              <div className={`w-full lg:w-1/2 order-2 lg:order-1 transition-all duration-700 ease-in-out ${
                activeStep === index ? 'opacity-100 transform translate-x-0' : 'opacity-50 transform translate-x-2'
              }`}>
                <div className="flex items-start space-x-4 lg:space-x-6 mb-6">
                  <div className="flex-shrink-0 w-12 h-12 lg:w-14 lg:h-14 rounded-full flex items-center justify-center text-white font-bold text-lg lg:text-xl bg-gradient-to-r from-purple-600 to-pink-600 shadow-lg">
                    {step.id}
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl lg:text-2xl font-bold mb-3 lg:mb-4 text-gray-900">
                      {step.title}
                    </h3>
                  </div>
                </div>

                <p className="text-base lg:text-lg leading-relaxed mb-6 text-gray-700">
                  {step.description}
                </p>

                {/* Additional features list */}
                <div className="space-y-2">
                  {index === 0 && (
                    <ul className="space-y-2 text-sm text-gray-600">
                      <li className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                        <span>Finance, Legal, Construction, and General templates</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                        <span>Pre-configured analysis parameters</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                        <span>Industry-specific insights</span>
                      </li>
                    </ul>
                  )}
                  {index === 1 && (
                    <ul className="space-y-2 text-sm text-gray-600">
                      <li className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                        <span>Intelligent section detection</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                        <span>Custom section selection</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                        <span>Focus on relevant content</span>
                      </li>
                    </ul>
                  )}
                  {index === 2 && (
                    <ul className="space-y-2 text-sm text-gray-600">
                      <li className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                        <span>Multiple language support</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                        <span>Accurate translations</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                        <span>Localized output format</span>
                      </li>
                    </ul>
                  )}
                  {index === 3 && (
                    <ul className="space-y-2 text-sm text-gray-600">
                      <li className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                        <span>Secure file upload</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                        <span>Multiple file formats</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                        <span>Up to 300 pages</span>
                      </li>
                    </ul>
                  )}
                  {index === 4 && (
                    <ul className="space-y-2 text-sm text-gray-600">
                      <li className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                        <span>Comprehensive analysis</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                        <span>Multiple preview formats</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                        <span>Export options</span>
                      </li>
                    </ul>
                  )}
                </div>
              </div>

              {/* Right side - Media (only for non-video steps) */}
              <div className="w-full lg:w-1/2 order-1 lg:order-2">
                <div className={`relative bg-gray-50 rounded-xl lg:rounded-2xl shadow-xl lg:shadow-2xl overflow-hidden border border-gray-200 transition-all duration-700 ease-in-out ${
                  activeStep === index
                    ? 'opacity-100 shadow-2xl'
                    : 'opacity-30 shadow-lg'
                }`}>
                  <div className="aspect-[4/3] relative p-3 lg:p-4 flex items-center justify-center bg-gray-50">
                    <img
                      src={step.image}
                      alt={step.imageAlt}
                      className="max-w-full max-h-full object-contain"
                      style={{ objectPosition: 'center' }}
                    />
                  </div>
                </div>
              </div>
                </>
              )}
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
