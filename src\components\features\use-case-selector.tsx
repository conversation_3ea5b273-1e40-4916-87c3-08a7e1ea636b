'use client'

import React, { useState } from 'react'
import { ChevronDown, ChevronUp, Shirt, Scale, GraduationCap, TrendingUp, FileText, Settings } from 'lucide-react'
import documentAnalysisConfig from '../../../config/document-analysis.config.json'

interface UseCaseSelectorProps {
  selectedUseCase: string | null
  onUseCaseChange: (useCaseId: string) => void
  className?: string
}

const iconMap = {
  Shirt,
  Scale,
  GraduationCap,
  TrendingUp,
  FileText,
  Settings
}

export function UseCaseSelector({ selectedUseCase, onUseCaseChange, className = '' }: UseCaseSelectorProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const useCases = documentAnalysisConfig.useCases

  // Define the exact order as specified in the config file
  const useCaseOrder = ['finance', 'office', 'legal', 'garment-bom', 'education', 'custom']

  // Create ordered array of use cases
  const orderedUseCases = useCaseOrder.map(id => [id, useCases[id as keyof typeof useCases]] as const)

  const selectedUseCaseData = selectedUseCase ? useCases[selectedUseCase as keyof typeof useCases] : null

  const handleUseCaseSelect = (useCaseId: string) => {
    onUseCaseChange(useCaseId)
    setIsExpanded(false)
  }

  return (
    <div className={`relative ${className}`}>
      {/* Selection Button */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className={`
          w-full p-4 bg-white border border-gray-300 rounded-lg transition-all duration-200 text-left
          ${isExpanded ? 'border-purple-500 shadow-lg ring-1 ring-purple-200' : 'hover:border-gray-400 hover:shadow-sm'}
          ${selectedUseCase ? 'border-purple-400 bg-purple-50/30' : ''}
        `}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {selectedUseCaseData ? (
              <>
                {React.createElement(iconMap[selectedUseCaseData.icon as keyof typeof iconMap], {
                  className: "w-5 h-5 text-purple-600"
                })}
                <div>
                  <div className="font-medium text-gray-900">{selectedUseCaseData.name}</div>
                  <div className="text-sm text-gray-500">{selectedUseCaseData.description}</div>
                </div>
              </>
            ) : (
              <>
                <div className="w-5 h-5 bg-gray-200 rounded-full"></div>
                <div>
                  {/* <div className="font-medium text-gray-500">Choose how you want to analyze your document</div> */}
                  <div className="text-md text-gray-400">Choose how you want to analyze your document</div>
                </div>
              </>
            )}
          </div>
          {isExpanded ? (
            <ChevronUp className="w-5 h-5 text-gray-400" />
          ) : (
            <ChevronDown className="w-5 h-5 text-gray-400" />
          )}
        </div>
      </button>

      {/* Dropdown Options */}
      {isExpanded && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-300 shadow-xl z-50 max-h-96 overflow-y-auto">
          {orderedUseCases.map(([useCaseId, useCase]) => {
            const Icon = iconMap[useCase.icon as keyof typeof iconMap]
            const isSelected = selectedUseCase === useCaseId
            
            return (
              <button
                key={useCaseId}
                onClick={() => handleUseCaseSelect(useCaseId)}
                className={`
                  w-full p-4 text-left transition-colors duration-150 border-b border-gray-100 last:border-b-0
                  ${isSelected 
                    ? 'bg-purple-50 border-purple-200' 
                    : 'hover:bg-gray-50'
                  }
                `}
              >
                <div className="flex items-start space-x-3">
                  <Icon className={`w-5 h-5 mt-0.5 ${isSelected ? 'text-purple-600' : 'text-gray-400'}`} />
                  <div className="flex-1">
                    <div className={`font-medium ${isSelected ? 'text-purple-900' : 'text-gray-900'}`}>
                      {useCase.name}
                    </div>
                    <div className={`text-sm mt-1 ${isSelected ? 'text-purple-700' : 'text-gray-500'}`}>
                      {useCase.description}
                    </div>
                    <div className="flex flex-wrap gap-1 mt-2">
                      {useCase.analysis_focus.slice(0, 3).map((focus, index) => (
                        <span
                          key={index}
                          className={`
                            text-xs px-2 py-1
                            ${isSelected
                              ? 'bg-purple-100 text-purple-700'
                              : 'bg-gray-100 text-gray-600'
                            }
                          `}
                        >
                          {focus}
                        </span>
                      ))}
                      {useCase.analysis_focus.length > 3 && (
                        <span className={`text-xs px-2 py-1 ${isSelected ? 'bg-purple-100 text-purple-700' : 'bg-gray-100 text-gray-600'}`}>
                          +{useCase.analysis_focus.length - 3} more
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </button>
            )
          })}
        </div>
      )}
    </div>
  )
}
